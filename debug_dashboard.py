#!/usr/bin/env python
"""
Debug script to reproduce the dashboard error
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
django.setup()

from accounts_app.models import CustomUser, ServiceProviderProfile
from venues_app.models import Venue
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from booking_cart_app.models import Booking, BookingItem
from accounts_app.models import TeamMember
from django.core.paginator import Paginator
from django.core.cache import cache
from django.db.models import Sum

def debug_dashboard_error():
    """Reproduce the dashboard error step by step"""
    print("=== Dashboard Debug Script ===")
    
    # Find a service provider user
    try:
        user = CustomUser.objects.filter(role='service_provider').first()
        if not user:
            print("ERROR: No service provider users found")
            return
        
        print(f"Testing with user: {user.email}")
        
        # Step 1: Get ServiceProviderProfile
        try:
            provider_profile = get_object_or_404(ServiceProviderProfile, user=user)
            print(f"✓ ServiceProviderProfile found: {provider_profile.legal_name}")
        except Exception as e:
            print(f"✗ ServiceProviderProfile error: {e}")
            return
        
        # Step 2: Get venue
        venue = getattr(provider_profile, 'venue', None)
        print(f"✓ Venue: {venue.venue_name if venue else 'None'}")
        
        if not venue:
            print("No venue - testing onboarding functions...")

            # Test onboarding functions
            try:
                from dashboard_app.views.provider import get_onboarding_progress, get_onboarding_checklist, get_feature_preview_cards, get_help_resources

                print("Testing get_onboarding_checklist()...")
                checklist = get_onboarding_checklist(provider_profile)
                print(f"✓ Checklist: {len(checklist)} items")

                print("Testing get_onboarding_progress()...")
                progress = get_onboarding_progress(provider_profile)
                print(f"✓ Progress: {progress}")

                print("Testing get_feature_preview_cards()...")
                feature_preview = get_feature_preview_cards()
                print(f"✓ Feature preview: {len(feature_preview)} cards")

                print("Testing get_help_resources()...")
                help_resources = get_help_resources()
                print(f"✓ Help resources: {len(help_resources.get('quick_actions', []))} actions")

                # Test logging function
                print("Testing log_dashboard_access()...")
                from dashboard_app.logging_utils import log_dashboard_access
                from django.test import RequestFactory

                factory = RequestFactory()
                request = factory.get('/dashboard/provider/')
                request.user = user

                log_dashboard_access(
                    user=user,
                    dashboard_type='provider_onboarding',
                    request=request,
                    details={
                        'has_venue': False,
                        'onboarding_mode': True,
                        'progress_percentage': progress['progress_percentage'],
                        'completed_steps': progress['completed_steps'],
                        'total_steps': progress['total_steps'],
                    }
                )
                print("✓ log_dashboard_access() works!")

                print("✓ All onboarding functions work!")

            except Exception as e:
                print(f"✗ Onboarding functions error: {e}")
                import traceback
                traceback.print_exc()

            return
        
        # Step 3: Test venue approval methods
        try:
            print("Testing venue.get_approval_requirements()...")
            approval_progress = venue.get_approval_requirements()
            print("✓ get_approval_requirements() worked")
        except Exception as e:
            print(f"✗ get_approval_requirements() error: {e}")
            import traceback
            traceback.print_exc()
            return
        
        try:
            print("Testing venue.can_submit_for_approval()...")
            can_submit_for_approval = venue.can_submit_for_approval()
            print(f"✓ can_submit_for_approval() = {can_submit_for_approval}")
        except Exception as e:
            print(f"✗ can_submit_for_approval() error: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # Step 4: Test booking queries
        try:
            today = timezone.now().date()
            print(f"Testing booking queries for date: {today}")
            
            todays_bookings = (
                Booking.objects.filter(
                    venue=venue,
                    items__scheduled_date=today
                )
                .select_related('customer')
                .prefetch_related('items', 'items__service')
                .distinct()
                .order_by('items__scheduled_time')[:10]
            )
            print(f"✓ Today's bookings: {todays_bookings.count()}")
            
            week_ago = today - timedelta(days=7)
            recent_qs = (
                Booking.objects.filter(
                    venue=venue,
                    booking_date__gte=week_ago
                )
                .select_related('customer')
                .prefetch_related('items', 'items__service')
                .order_by('-booking_date')
            )
            print(f"✓ Recent bookings query: {recent_qs.count()}")
            
            # Test pagination
            paginator = Paginator(recent_qs, 5)
            recent_bookings = paginator.get_page(1)
            print(f"✓ Pagination worked: {len(recent_bookings)}")
            
        except Exception as e:
            print(f"✗ Booking queries error: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # Step 5: Test booking counts
        try:
            total_bookings = Booking.objects.filter(venue=venue).count()
            pending_bookings = Booking.objects.filter(venue=venue, status='pending').count()
            confirmed_bookings = Booking.objects.filter(venue=venue, status='confirmed').count()
            completed_bookings = Booking.objects.filter(venue=venue, status='completed').count()
            print(f"✓ Booking counts: total={total_bookings}, pending={pending_bookings}, confirmed={confirmed_bookings}, completed={completed_bookings}")
        except Exception as e:
            print(f"✗ Booking counts error: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # Step 6: Test earnings calculation
        try:
            current_month_start = today.replace(day=1)
            monthly_earnings = BookingItem.objects.filter(
                booking__venue=venue,
                booking__status__in=['confirmed', 'completed'],
                scheduled_date__gte=current_month_start
            ).aggregate(total=Sum('service_price'))['total'] or Decimal('0.00')
            print(f"✓ Monthly earnings: {monthly_earnings}")
        except Exception as e:
            print(f"✗ Monthly earnings error: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # Step 7: Test team members count
        try:
            team_members_count = TeamMember.objects.filter(
                service_provider=provider_profile,
                is_active=True
            ).count()
            print(f"✓ Team members count: {team_members_count}")
        except Exception as e:
            print(f"✗ Team members count error: {e}")
            import traceback
            traceback.print_exc()
            return
        
        print("\n=== All tests passed! Dashboard should work ===")
        
    except Exception as e:
        print(f"FATAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_dashboard_error()
